
function DrawText3D(x, y, z, text)
    local onScreen,_x,_y = World3dToScreen2d(x,y,z)
    local px,py,pz = table.unpack(GetGameplayCamCoords())
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x,_y)
    local factor = (string.len(text)) / 370
    DrawRect(_x,_y+0.0125, 0.015 + factor, 0.03, 0, 0, 0, 75)
end


local Code = [[
    ESX = nil

Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(0)
    end
end)

Citizen.CreateThread(function()
    for _,v in pairs(Config.GarageCoords) do    
        RequestModel(v.npcHash)
        while not HasModelLoaded(v.npcHash) do
          Wait(1)
        end
        ped =  CreatePed(4,v.npcHash, v.npc.x,v.npc.y,v.npc.z-1, 3374176, false, true)
        SetEntityHeading(ped,v.npcHeading)
        FreezeEntityPosition(ped, true)
        SetEntityInvincible(ped, true)
        SetBlockingOfNonTemporaryEvents(ped, true)
    end
end)

local tables = {}

CreateThread(function()
    Wait(500)
    ESX.TriggerServerCallback('xC_Garage:loadvehicles', function(data)
        if #data == 0 then
            --print('Vehicles table missing..')
        end            
    end)
end)

RegisterNetEvent('notfiycheck')
AddEventHandler('notfiycheck', function(par1,par2)   
    SendNUIMessage({
        action = 'notify',
        message = par1,
        type = par2,
    })
end)

local lastgarage 
local garagetype 
local newstored 
local vehicleprice 
local arabaDisarda = true
local impound  = {}
local press1 = true
local press2 = false



Citizen.CreateThread(function()
        while true do
        local Sleep = 2000
        local PlayerPed = PlayerPedId()
        local PlayerCoord = GetEntityCoords(PlayerPed)
        local BabatMord = GetVehiclePedIsIn(PlayerPed)
        local ArabaPlaka = ESX.Game.GetVehicleProperties(BabatMord).plate
        
        local playerPed    = GetPlayerPed(-1)
		local coords       = GetEntityCoords(playerPed)
		local vehicle      = GetVehiclePedIsIn(playerPed, false)
		local vehicleProps = ESX.Game.GetVehicleProperties(vehicle)
		local current 	   = GetPlayersLastVehicle(GetPlayerPed(-1), true)
		local engineHealth = GetVehicleEngineHealth(current)
		local plate        = vehicleProps.plate
		
        for k,v in pairs(Config.GarageCoords) do
          
            local Distance = #(PlayerCoord - v.parking) 

            if Distance <= 10.0 then
                if IsPedInAnyVehicle(PlayerPedId(),false)  then 
                    Sleep = 5
                    
                    ESX.ShowHelpNotification('Press ~INPUT_PICKUP~ To Park Vehicle')
                    DrawMarker(1, v.parking, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 6.0, 6.0, 0.5, 255 , 0 , 0 , 255 , false, false, false, 1, false, false, false)
                    if IsControlJustReleased(0, 38) then
                        ESX.TriggerServerCallback('xC_Garage:vehicleOwned', function(owned)
                            if owned then
                                if engineHealth < 990 then
                                    reparation(apprasial, vehicle, vehicleProps)
                                else
                                    TriggerServerEvent('xC_Garage:saveProps', ArabaPlaka, ESX.Game.GetVehicleProperties(BabatMord))
                                    TaskLeaveVehicle(PlayerPed, BabatMord, 0)
                                    Citizen.Wait(2500)
                                    ESX.Game.DeleteVehicle(BabatMord)
                                    TriggerServerEvent('xC_Garage:stored', ArabaPlaka, 1)
                                end
                            else
                                ESX.ShowNotification('~r~In Mashin Shoma Nist')
                            end

                        end, ArabaPlaka)
                    end
                end
            elseif Distance <= 20.0 then
                Sleep = 5
                DrawMarker(1, v.x, v.y, v.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 6.0, 6.0, 0.30, 120, 10, 20, 155, false, false, false, 1, false, false, false)
            end       
            if v.garageType == 'normal' then
                local dist = #(PlayerCoord - v.npc )
                if dist <= 3.0 then
                    Sleep = 0
					DrawText3D(v.npc,'Press ~INPUT_PICKUP~ To Open Garage'.x, v.npc,'Press ~INPUT_PICKUP~ To Open Garage'.y, v.npc,'Press ~INPUT_PICKUP~ To Open Garage'.z + 1.0, 'Press ~INPUT_PICKUP~ To Open Garage')
                    if IsControlJustReleased(0,38) then
                        press1 = false
                        press2 = true
                        lastgarage = k
                        garagetype = 1
                        ESX.TriggerServerCallback('xC_Garage:loadvehicles', function(veri)
                            if #veri == 0 then
                                ESX.ShowNotification('~r~Shoma Mashini Dakhel Garage Nadarid')
                            else                              
                                for _,v in pairs(veri) do 
                                    if v.stored then
                                        newstored = 1                                      
                                    else
                                        newstored = 0
                                    end                                  
                                    local carhash = v.vehicle.model
                                    local damage = "100"                      
                                    local carname = GetDisplayNameFromVehicleModel(carhash)                                       
                                    local spawnkod = string.gsub(carname, "%s+", ""):lower();
                                    local vehicleName = GetLabelText(carname)                                          
                                    local brand = Config.VehiclesManufacturer[spawnkod] or Config.DefaultManufacturer
                                    local fuellevel = '75'
                                    local plate = v.plate   
                                    local body =  "1000"
                                    if tables[carhash] ~= nil then 
                                        local price = tables[carhash]
                                        vehicleprice = price.price / 2 
                                    else
                                        vehicleprice = 0
                                    end                                                       
                                    AddCar(newstored,body,carhash,plate, spawnkod,vehicleName,damage,fuellevel,brand,vehicleprice,garagetype,sellcar,transferCar)
                                end                              
                            end    
                        end)
                        Citizen.Wait(1000)
                    end
                end
               
            elseif v.garageType == 'impound' then
                local dist = #(PlayerCoord - v.npc )
                    if dist <= 3.0 then
                        Sleep = 0
                        DrawText3D(v.npc,'Press ~INPUT_PICKUP~ To Open Impound Garage'.x, v.npc,'Press ~INPUT_PICKUP~ To Open Impound Garage'.y, v.npc,'Press ~INPUT_PICKUP~ To Open Impound Garage'.z + 1.0, 'Press ~INPUT_PICKUP~ To Open Garage')
                        if IsControlJustReleased(0,38) then
                            lastgarage = k
                            garagetype = 0
                            ESX.TriggerServerCallback('xC_Garage:impoundGarage', function(veri)
                                if #veri == 0 then
                                else
                                    impound = {}
                                    for _,v in pairs(veri) do                               
                                        if not  v.stored then
                                            ESX.TriggerServerCallback('xC_Garage:changedCars', function(veri2)                              
                                            if veri2 then
                                                impound[v.plate] = true           
                                            else
                                                impound[v.plate] = false
                                            end
                                        end,v.plate)
                                        while impound[v.plate] == nil do
                                            Citizen.Wait(0)
                                        end
                                        local carhash = v.vehicle.model
                                        local damage = "100"                   
                                        local carname = GetDisplayNameFromVehicleModel(carhash)                                       
                                        local spawnkod = string.gsub(carname, "%s+", ""):lower();
                                        local vehicleName = GetLabelText(carname)                                          
                                        local brand = Config.VehiclesManufacturer[spawnkod] or Config.DefaultManufacturer
                                        local fuellevel = '75'
                                        local plate = v.plate   
                                        local body =  "1000"
                                        if tables[carhash] ~= nil then 
                                            local price = tables[carhash]
                                            vehicleprice = price.price / 2 
                                        else
                                            vehicleprice = 0
                                        end              
                                                                                                                                                                                                                                              
                                        AddCar(newstored,body,carhash,plate, spawnkod,vehicleName,damage,fuellevel,brand,vehicleprice,garagetype,sellcar,transferCar,impound[v.plate])
                                    end 
                                end 
                            end    
                        end)
                        Citizen.Wait(1000)
                    end
                end                
            end
        end
        Citizen.Wait(Sleep)
    end
end)

RegisterNUICallback('closepage',function()
    SetNuiFocus(false,false)
end)

RegisterNUICallback('spawnvehicle',function(data,cb)
    ESX.TriggerServerCallback('xC_Garage:checkMoneyCars', function(hasEnoughMoney)
        if hasEnoughMoney then
            ESX.TriggerServerCallback('xC_Garage:vehicleOwner', function(cars)
                local x,y,z = table.unpack(Config.GarageCoords[lastgarage]["parkout"])
                local props = json.decode(cars[1].vehicle)
                ESX.Game.SpawnVehicle(props.model, {
                   x = x,
                   y = y,
                   z = z + 1
                }, Config.GarageCoords[lastgarage]["vehicleHeading"], function(callback_vehicle)
                blipxdcar = AddBlipForEntity(callback_vehicle)
                SetBlipSprite(blipxdcar, 724)
                SetBlipDisplay(blipxdcar, 4)
                SetBlipScale(blipxdcar, 0.9)
                BeginTextCommandSetBlipName("STRING")
                AddTextComponentString(GetDisplayNameFromVehicleModel(GetEntityModel(callback_vehicle))..' ~r~|~b~ '..data.plate)
                EndTextCommandSetBlipName(blipxdcar)
                SetVehicleNumberPlateText(callback_vehicle, data.plate)
		        ESX.ShowNotification("~y~GPS ~r~"..GetLabelText(GetDisplayNameFromVehicleModel(GetEntityModel(callback_vehicle))).."~w~ Enable Shod!")
                ESX.Game.SetVehicleProperties(callback_vehicle, props)
                SetVehRadioStation(callback_vehicle, "OFF")
                TaskWarpPedIntoVehicle(GetPlayerPed(-1), callback_vehicle, -1)
                if garagetype then
                    TriggerServerEvent('xC_Garage:payCar', garagetype)
                else 
                    TriggerServerEvent('xC_Garage:payCar',garagetype)
                end
                TriggerServerEvent('xC_CreateKey', "CarKey|"..data.plate, "CarKey | "..data.plate, 1, 0, 0);
				TriggerServerEvent("xC_CarLock:ToggleKey", true, data.plate, Veh)
            end)
           
           end,data.plate)
           TriggerServerEvent('xC_Garage:stored', data.plate, 0)
        else
            ESX.ShowNotification('~r~Shoma Pool Kafi Barai Spwan Mashin Nadarid ~g~($'..Config.Price..')')
        end
    end,garagetype) 
end)

function AddCar(stored,body,carhash,plate, model,name,damage,fuellevel,brand,vehicleprice,garagetype,sellcar,transferCar,impound)
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = 'open',             
        stored = stored,
        body = body,
        carhash = carhash,
        plate = plate,
        model = model,
        damage = damage,
        name = name,
        fuellevel = fuellevel,
        brand = brand,
        vehicleprice = vehicleprice,
        garaj = garagetype,
        sellcar = sellcar,
        transferCar = transferCar,
        impound = impound       
    }) 
end

Citizen.CreateThread(function()
    for _, coords in pairs(Config.GarageCoords) do
        local blip = AddBlipForCoord(coords.npc)
        SetBlipSprite(blip, 357)
        SetBlipScale(blip, 0.9) 
        SetBlipColour(blip, 32)
        SetBlipDisplay(blip, 4)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(coords.garageName)
        EndTextCommandSetBlipName(blip)
    end
end)

-- Repair Vehicles
function reparation(apprasial, vehicle, vehicleProps)
	ESX.UI.Menu.CloseAll()
    local PlayerPed = PlayerPedId()
    local PlayerCoord = GetEntityCoords(PlayerPed)
    local BabatMord = GetVehiclePedIsIn(PlayerPed)
    local ArabaPlaka = ESX.Game.GetVehicleProperties(BabatMord).plate
    
	local elements = {
		{label = 'Tamir Mashin Va Pardakht Pool', value = 'yes'},
		{label = 'Bordan Be Mechanici', value = 'no'}
	}
	
	ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'delete_menu', {
		title    = 'Damage Mashin',
		align    = 'left',
		elements = elements
	}, function(data, menu)
		menu.close()
		
		if data.current.value == 'yes' then

			ESX.TriggerServerCallback('xC_Garage:checkRepairCost', function(hasEnoughMoney)
				if hasEnoughMoney then
					TriggerServerEvent('xC_Garage:payhealth', apprasial)
                    TriggerServerEvent('xC_Garage:saveProps', ArabaPlaka, ESX.Game.GetVehicleProperties(BabatMord))
                    TaskLeaveVehicle(PlayerPed, BabatMord, 0)
                    Citizen.Wait(2500)
                    ESX.Game.DeleteVehicle(BabatMord)
                    TriggerServerEvent('xC_Garage:stored', ArabaPlaka, 1)
				else
                    ESX.ShowNotification('~r~Shoma Pool Kafi Barai Tamir Mashin Nadari')

				end
			end, tonumber(apprasial))

		elseif data.current.value == 'no' then
            ESX.ShowNotification('~y~Lotfan Mashin Ra Be Mechanici Bebarid')
		end
	end, function(data, menu)
		menu.close()
	end)
end


RegisterNetEvent('xC_Garage:DeleteAllVehicle')
AddEventHandler('xC_Garage:DeleteAllVehicle', function()
	local vehicles = ESX.Game.GetVehicles()
	for _,entity in ipairs(vehicles) do

		carModel = GetEntityModel(entity)
		carName = GetDisplayNameFromVehicleModel(carModel)
		NetworkRequestControlOfEntity(entity)
		
		local timeout = 2000
		while timeout > 0 and not NetworkHasControlOfEntity(entity) do
			Citizen.Wait(100)
			timeout = timeout - 100
		end

		SetEntityAsMissionEntity(entity, true, true)
		
		local timeout = 2000
		while timeout > 0 and not IsEntityAMissionEntity(entity) do
			Citizen.Wait(100)
			timeout = timeout - 100
		end

		if IsVehicleSeatFree(entity, -1) then
			DeleteVehicle(entity)
			-- Citizen.InvokeNative( 0xEA386986E786A54F, Citizen.PointerValueIntInitialized( entity ) )
			
			if (DoesEntityExist(entity)) then 
				DeleteEntity(entity)
			end
		end
	end

end)
]]

local Table = {}
RegisterServerEvent('xC_AntiDumpGarage:GetSc')
AddEventHandler('xC_AntiDumpGarage:GetSc', function()
    if Table[source] == nil then
        Table[source] = true
        TriggerClientEvent("xC_AntiDumpGarage:GetSc", source, Code)
    else
        --Salam
    end
end)

ESX = nil
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
function ExecuteSql(query)
    local IsBusy = true
    local result = nil
    if Config.Mysql == "oxmysql" then
        if MySQL == nil then
            exports.oxmysql:execute(query, function(data)
                result = data
                IsBusy = false
            end)
        else
            MySQL.query(query, {}, function(data)
                result = data
                IsBusy = false
            end)
        end
    elseif Config.Mysql == "ghmattimysql" then
        exports.ghmattimysql:execute(query, {}, function(data)
            result = data
            IsBusy = false
        end)
    elseif Config.Mysql == "mysql-async" then
        MySQL.Async.fetchAll(query, {}, function(data)
            result = data
            IsBusy = false
        end)
    end
    while IsBusy do Citizen.Wait(0) end
    return result
end

local vehiclepricetables = {}
Citizen.CreateThread(function()
    
    local cars = ExecuteSql("SELECT * FROM vehicles ")
    if cars then
        for k, v in pairs(cars) do v.hash = GetHashKey(v.model) end
        vehiclepricetables = cars
    else
        --print('VEHICLES the specified table does not exist in sql')
        --print('VEHICLES the specified table does not exist in sql')
        --print('VEHICLES the specified table does not exist in sql')
        --print('VEHICLES the specified table does not exist in sql')
        --print('VEHICLES the specified table does not exist in sql')
        --print('VEHICLES the specified table does not exist in sql')
        --print('VEHICLES the specified table does not exist in sql')
        --print('VEHICLES the specified table does not exist in sql')
    end
end)

RegisterServerEvent('xC_Garage:payhealth')
AddEventHandler('xC_Garage:payhealth', function(price)
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(15000)
    TriggerClientEvent("esx:showNotification", source,'Shoma $15000 Pardakht Kardid Barai Park Mashin Va Tamir')

end)

ESX.RegisterServerCallback('xC_Garage:checkRepairCost', function(source, cb, fee)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.get('money') >= 15000 then
		cb(true)
	else
		cb(false)
	end
end)

ESX.RegisterServerCallback('xC_Garage:loadvehicles',function(source, cb)
	cb(vehiclepricetables) 
end)

local storedsql = 1

AddEventHandler('onResourceStart', function(resource)
    if resource == GetCurrentResourceName() then
        Wait(100)
        if Config.Framework == 'esx' then
            if Config.Mysql == 'oxmysql' then
                ExecuteSql("UPDATE  `owned_vehicles` SET `stored` = '" ..storedsql .. "'")
            elseif Config.Mysql == 'mysql-async' then
                local sqlverisi = ExecuteSql("UPDATE  `owned_vehicles` SET `stored` = '" .. storedsql .. "'")
                --print('OWNED VEHİCLES  sql data updated')
                --print('OWNED VEHİCLES  sql data updated')
                --print('OWNED VEHİCLES  sql data updated')
            end
        else
            if Config.Mysql == 'oxmysql' then
                local sqlverisi = ExecuteSql("UPDATE  `player_vehicles` SET `state` = '" .. storedsql .. "'")
            elseif Config.Mysql == 'mysql-async' then
                local sqlverisi = ExecuteSql("UPDATE  `player_vehicles` SET `state` = '" .. storedsql .. "'")
            end
        end
    end
end)

ESX.RegisterServerCallback('xC_Garage:loadvehicles', function(source, cb)
    local ownedCars = {}
    local xPlayer = ESX.GetPlayerFromId(source)
    local vehicles = ExecuteSql("SELECT * FROM owned_vehicles WHERE `owner` ='" .. xPlayer.identifier .. "' AND `type`= 'car'")
    for _, v in pairs(vehicles) do
        local vehicle = json.decode(v.vehicle)
        table.insert(ownedCars,{vehicle = vehicle, stored = v.stored, plate = v.plate})
    end
    cb(ownedCars)
end)

ESX.RegisterServerCallback('xC_Garage:impoundGarage', function(source, cb)
    local impoundCars = {}
    local identifier = ESX.GetPlayerFromId(source)
    local vehicles = ExecuteSql( "SELECT * FROM owned_vehicles WHERE `owner` ='" .. identifier.identifier .. "' OR `owner` = '"..identifier.gang.name.."'")
    for _, v in pairs(vehicles) do
        local vehicle = json.decode(v.vehicle)
        table.insert(impoundCars, {vehicle = vehicle, stored = v.stored, plate = v.plate})
    end
    cb(impoundCars)
end)

ESX.RegisterServerCallback('xC_Garage:vehicleOwner',function(source, cb, plate)
    local cars = ExecuteSql("SELECT * FROM owned_vehicles WHERE `plate` ='" .. plate .. "'")
    cb(cars)
end)

ESX.RegisterServerCallback('xC_Garage:vehicleOwned',function(source, cb, plate)
    local xPlayer = ESX.GetPlayerFromId(source)
    local vehicle = ExecuteSql( "SELECT `vehicle` FROM `owned_vehicles` WHERE `plate` = '" .. plate .. "' AND `owner` = '" .. xPlayer.identifier .. "'")
    if next(vehicle) then
        cb(true)
    else
        cb(false)
    end
end)

ESX.RegisterServerCallback('xC_Garage:changedCars',function(source, cb, plate)
    local vehicles = GetAllVehicles()
    plate = ESX.Math.Trim(plate)
    local found = false
    for i = 1, #vehicles do
        if ESX.Math.Trim(GetVehicleNumberPlateText(vehicles[i])) == plate then

            found = true
            break
        end
    end
    cb(found)
end)

RegisterNetEvent('xC_Garage:saveProps')
AddEventHandler('xC_Garage:saveProps', function(plate, props)
    local xProps = json.encode(props)
    ExecuteSql("UPDATE  `owned_vehicles` SET `vehicle` = '" .. xProps .. "' WHERE `plate` ='" .. plate .. "'")

end)

ESX.RegisterServerCallback('xC_Garage:checkMoneyCars',function(source, cb, garagetype)
    local identifier = ESX.GetPlayerFromId(source)
    if garagetype then
        if identifier.money >= Config.Price then
            cb(true)
        else
            cb(false)
        end
    else
        if identifier.money >= Config.impoundPrice then
            cb(true)
        else
            cb(false)
        end
    end

end)

RegisterServerEvent('xC_Garage:payCar')
AddEventHandler('xC_Garage:payCar', function(par1)
    local identifier = ESX.GetPlayerFromId(source)
    if par1 then
        if Config.Account == 'money' then
            identifier.removeMoney(Config.Price)
            TriggerClientEvent("esx:showNotification", source,Config.Notifications["parkprice"].message .. Config.Price, Config.Notifications["parkprice"].type)

        else
            identifier.removeMoney('bank', Config.Price)
            TriggerClientEvent("esx:showNotification", source,Config.Notifications["parkprice"].message ..Config.Price,Config.Notifications["parkprice"].type)

        end

    else
        if Config.Account == 'bank' then

            identifier.removeMoney('bank', Config.impoundPrice)
            TriggerClientEvent("esx:showNotification", source, Config.Notifications["parkprice"].message .. Config.impoundPrice, Config.Notifications["parkprice"].type)

        else

            identifier.removeMoney('bank', Config.impoundPrice)
            TriggerClientEvent("esx:showNotification", source,Config.Notifications["parkprice"].message ..Config.impoundPrice, Config.Notifications["parkprice"].type)
        end

    end
end)

RegisterNetEvent('xC_Garage:stored')
AddEventHandler('xC_Garage:stored', function(plate, state)
    MySQL.Async.fetchAll("UPDATE  `owned_vehicles` SET `stored` = '" .. state .. "' WHERE `plate` ='" .. plate .. "'",{
        }, function (result)
    end)
end)

function GetIdentifier(source)
    local xPlayer = ESX.GetPlayerFromId(tonumber(source))
    return xPlayer.Identifier
end

function getid(source, parametre2)

    local identifier = {}
    local identifiers = {}

    identifiers = GetPlayerIdentifiers(source)

    for i = 1, #identifiers do
        if string.match(identifiers[i], "discord:") then
            identifier["discord"] = string.sub(identifiers[i], 9)
            identifier["discord"] = "<@" .. identifier["discord"] .. ">"
        end
        if parametre2 == 'esx' then
            if string.match(identifiers[i], "steam:") then
                identifier["license"] = identifiers[i]
            end
        else
            if string.match(identifiers[i], "license:") then
                identifier["license"] = identifiers[i]
            end

        end
    end
    if identifier["discord"] == nil then identifier["discord"] = "Bilinmiyor" end
    return identifier
end

function dclog(webhook, title, text, target)
    local ts = os.time()
    local time = os.date('%Y-%m-%d %H:%M:%S', ts)
    local connect = {
        {
            ["color"] = 3092790,
            ["title"] = title,
            ["description"] = text,
            ["footer"] = {
                ["text"] = "CodeM Store      " .. time,
                ["icon_url"] = Config.IconURL
            }
        }
    }
    PerformHttpRequest(Config.TransferWeebhok, function(err, text, headers) end, 'POST', json.encode({
        username = Config.Botname,
        embeds = connect,
        avatar_url = Config.Logo
    }), {['Content-Type'] = 'application/json'})
end


-- Store Vehicles
ESX.RegisterServerCallback('xC_Garage:storeVehicle', function (source, cb, vehicleProps)
	local ownedCars = {}
	local vehplate = vehicleProps.plate:match("^%s*(.-)%s*$")
	local vehiclemodel = vehicleProps.model
	local xPlayer = ESX.GetPlayerFromId(source)
	local path = GetResourcePath(GetCurrentResourceName()).."/v"
	local file = io.open(path, 'a+')
	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE (owner = @player OR LOWER(`owner`) = @gang) AND plate = @plate', {
		['@player'] = xPlayer.identifier,
		['@gang'] = string.lower(xPlayer.gang.name),
		['@plate'] = vehicleProps.plate
	}, function (result)
		if result[1] ~= nil then
			local originalvehprops = json.decode(result[1].vehicle)
			if originalvehprops.model == vehiclemodel then
				MySQL.Async.execute('UPDATE owned_vehicles SET vehicle = @vehicle WHERE (owner = @player OR LOWER(`owner`) = @gang) AND plate = @plate', {
					['@player'] = xPlayer.identifier,
					['@gang'] = string.lower(xPlayer.gang.name),
					['@vehicle'] = json.encode(vehicleProps),
					['@plate']  = vehicleProps.plate
				}, function (rowsChanged)
					if rowsChanged == 0 then
					end
					cb(true)
				end)
			else
				TriggerEvent('Anticheat:AutoBan', source, {period = -1, reason = 'Taqire Hash Mashin'})
			end
		else
			cb(false)
		end
	end)
end)

-- Fetch Pounded Cars
ESX.RegisterServerCallback('xC_Garage:getOutOwnedCars', function(source, cb)
	local ownedCars = {}
	xPlayer = ESX.GetPlayerFromId(source)
	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE (owner = @player or LOWER(`owner`) = @gang) AND Type = @Type AND `stored` = @stored', {
		['@player'] = xPlayer.identifier,
		['@gang'] 	= string.lower(xPlayer.gang.name),
		['@Type']   = 'car',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedCars, vehicle)
		end
		cb(ownedCars)
	end)
end)

